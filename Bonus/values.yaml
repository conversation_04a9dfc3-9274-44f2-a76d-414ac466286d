certmanager-issuer:
  email: <EMAIL>

global:
  hosts:
    domain: local
    https: false
    externalIP:  # Will be auto-detected from k3d LoadBalancer
    gitlab:
      name: gitlab.local
    minio:
      name: minio.local
    registry:
      name: registry.local
    kas:
      name: kas.local

  ingress:
    configureCertmanager: false
    enabled: true
    tls:
      enabled: false
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "false"

  # Use Community Edition for simpler setup
  edition: ce

  # Disable TLS for various components
  gitaly:
    tls:
      enabled: false

  registry:
    api:
      protocol: http
    tls:
      enabled: false

  kas:
    tls:
      enabled: false

  workhorse:
    tls:
      enabled: false

# Disable cert-manager installation
installCertmanager: false

# Configure nginx-ingress for HTTP-only
nginx-ingress:
  enabled: true
  controller:
    config:
      ssl-redirect: "false"
      force-ssl-redirect: "false"
      use-http2: "false"
    service:
      type: LoadBalancer

# Configure GitLab components for HTTP
gitlab:
  webservice:
    ingress:
      tls:
        enabled: false

  gitlab-shell:
    service:
      type: LoadBalancer
      port: 22
