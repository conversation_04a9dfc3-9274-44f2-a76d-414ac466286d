apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: playground-app-v1
  namespace: argocd
spec:
  project: default
  source:
    repoURL: https://github.com/0xTheFr34k/inception-app.git
    targetRevision: main
    path: v1
  destination:
    server: https://kubernetes.default.svc
    namespace: dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
    - CreateNamespace=true
---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: playground-app-v2
  namespace: argocd
spec:
  project: default
  source:
    repoURL: https://github.com/0xTheFr34k/inception-app.git
    targetRevision: main
    path: v2
  destination:
    server: https://kubernetes.default.svc
    namespace: dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
    - CreateNamespace=true

