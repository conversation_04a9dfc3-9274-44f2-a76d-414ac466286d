# GitLab Helm Chart Values for HTTP-only deployment in k3d
# This configuration disables HTTPS/TLS and runs GitLab on HTTP only

global:
  # Configure host settings for HTTP-only access
  hosts:
    domain: gitlab.local  # Change this to your preferred domain
    https: false          # Disable HTTPS
    externalIP:          # Will be auto-detected from k3d LoadBalancer
  
  # Disable ingress TLS configuration
  ingress:
    configureCertmanager: false  # Disable cert-manager
    enabled: true
    tls:
      enabled: false            # Disable TLS on ingress
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "false"  # Prevent HTTP to HTTPS redirect
  
  # Configure GitLab edition (ce for Community Edition, ee for Enterprise Edition)
  edition: ce  # Use Community Edition for simpler setup
  
  # Disable various TLS configurations
  gitaly:
    tls:
      enabled: false
  
  praefect:
    tls:
      enabled: false
  
  registry:
    api:
      protocol: http  # Use HTTP for registry API
    tls:
      enabled: false
  
  kas:
    tls:
      enabled: false
  
  workhorse:
    tls:
      enabled: false

# Disable cert-manager installation since we don't need TLS certificates
installCertmanager: false

# Configure nginx-ingress for HTTP-only
nginx-ingress:
  enabled: true
  controller:
    config:
      # Disable SSL redirect and force HTTP
      ssl-redirect: "false"
      force-ssl-redirect: "false"
      use-http2: "false"  # Disable HTTP/2 which requires TLS
    service:
      # Use LoadBalancer type for k3d
      type: LoadBalancer
      # Configure HTTP port only
      ports:
        http: 80
      targetPorts:
        http: 80

# Configure GitLab components
gitlab:
  webservice:
    ingress:
      tls:
        enabled: false
  
  gitlab-shell:
    service:
      type: LoadBalancer  # Expose SSH via LoadBalancer for k3d
      port: 22

# Reduce resource requirements for local development
redis:
  master:
    resources:
      requests:
        memory: 256Mi
        cpu: 100m

postgresql:
  primary:
    resources:
      requests:
        memory: 512Mi
        cpu: 250m

# Configure MinIO for HTTP
minio:
  ingress:
    tls:
      enabled: false
