apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: argocd-server
  namespace: argocd
spec:
  entryPoints:
    - websecure
    - web
  routes:
    - kind: Rule
      match: Host(`argocd.local`)
      services:
        - name: argocd-server
          port: 80
    - kind: Rule
      match: Host(`argocd.local`) && Header(`Content-Type`, `application/grpc`)
      services:
        - name: argocd-server
          port: 80
          scheme: h2c
  tls:
    certResolver: default

