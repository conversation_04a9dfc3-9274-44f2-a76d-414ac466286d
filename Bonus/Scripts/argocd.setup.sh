k3d cluster delete Gitlab
k3d cluster create -c Confs/GitlabCluster.yml

kubectl create namespace gitlab

helm repo add gitlab https://charts.gitlab.io/

helm repo update

helm install gitlab gitlab/gitlab --namespace gitlab -f values.yaml

# kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml
#
# kubectl patch configmap argocd-cmd-params-cm -n argocd --patch-file Confs/argocd-cmd-params-cm.yml
#
# until [ "$(kubectl get pods -n argocd -l app.kubernetes.io/name=argocd-server -o jsonpath='{.items[0].status.phase}')" = "Running" ]; do
#   echo "Waiting for argocd-server to be Running..."
#   sleep 2
# done
#
# kubectl apply -f Confs/ingress_argocd.yml
# argocd admin initial-password -n argocd
#
# kubectl apply -f Confs/applications.yaml
